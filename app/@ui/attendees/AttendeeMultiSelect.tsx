import { Badge } from "~/@shadcn/ui/badge";
import { <PERSON><PERSON> } from "~/@shadcn/ui/button";
import { Popover, PopoverTrigger, PopoverContent } from "~/@shadcn/ui/popover";
import { cn } from "~/@shadcn/utils";
import { PlusIcon, CheckIcon, CaretSortIcon } from "@radix-ui/react-icons";
import {
  ContactsSharp,
  Help,
  Person4Sharp,
  SearchOutlined,
} from "@mui/icons-material";
import { Typography } from "~/@ui/Typography";
import { Input } from "~/@shadcn/ui/input";
import { Separator } from "~/@shadcn/ui/separator";
import { AttendeeOption, AttendeeOptions } from "~/api/attendees/types";
import { AttendeeOptionTag, tagOptions } from "./attendeeTags";
import {
  ReactNode,
  Dispatch,
  SetStateAction,
  useState,
  useRef,
  useMemo,
  useEffect,
  useCallback,
} from "react";
import { useFuzzySearchList, Highlight } from "@nozbe/microfuzz/react.js";
import { useFlag } from "~/context/flags";
import { FuzzyMatches } from "@nozbe/microfuzz";
import { FixedSizeList } from "react-window";

type TagOptionKey = keyof typeof tagOptions;

type Props = {
  initialOptions: AttendeeOption[];
  selected: AttendeeOptions;
  placeholder?: string;
  emptyLabel?: string;
  leftIcon?: ReactNode;
  onChange: Dispatch<SetStateAction<AttendeeOptions>>;
  commandClassName?: string;
  triggerClassName?: string;
  allowNew?: boolean;
  modal?: boolean;
  "aria-describedby"?: string;
};

const attendeeTagTitle = (
  attendee: AttendeeOption,
  showClientType: boolean
) => {
  let title: string | undefined = attendee.type;
  if (!title) {
    return "";
  }
  if (showClientType && attendee.type === "client" && attendee.clientType) {
    title = title.concat(` (${attendee.clientType})`);
  }
  return title;
};

export const AttendeesMultiSelect = ({
  initialOptions,
  selected,
  placeholder = "Select an item",
  emptyLabel = "No item found.",
  leftIcon,
  onChange,
  commandClassName,
  triggerClassName,
  allowNew = true,
  modal = false,
  "aria-describedby": ariaDescribedBy,
}: Props) => {
  const [open, setOpen] = useState(false);
  const [options, setOptions] = useState<AttendeeOptions>(initialOptions);
  const [searchTerm, setSearchTerm] = useState("");
  const searchInputRef = useRef<HTMLInputElement>(null);
  const enableShowClientType =
    useFlag("ShowClientTypesInAttendeesList") ?? false;
  // The height of each item in the virtualized list, in pixels
  const itemSize = 40;

  // The maximum height of the list before it starts scrolling, in pixels.
  const maxListHeight = 400;

  const mapResultItem = useCallback(
    ({
      item,
      score,
      matches: [highlightRanges],
    }: {
      item: AttendeeOption;
      score: number;
      matches: FuzzyMatches;
    }) => ({
      item,
      highlightRanges,
    }),
    []
  );

  const filteredOptions = useFuzzySearchList({
    list: options,
    queryText: searchTerm,
    key: "name",
    mapResultItem,
  });

  const searchTermTrimmed = searchTerm.trim();

  const clearInputAndFocus = () => {
    // Clear search input, then focus it again
    if (searchInputRef.current) {
      setSearchTerm("");
      searchInputRef.current.value = "";
      searchInputRef.current.focus();
    }
  };

  const addNewOption = () => {
    const newOptionToAdd: AttendeeOption = {
      name: searchTermTrimmed,
      uuid: crypto.randomUUID(),
      type: "unknown",
    };
    setOptions((prev) => prev.concat(newOptionToAdd));
    onChange([...selected, newOptionToAdd]);
    setOpen(true);
  };

  const toggleOptionSelection = (option: AttendeeOption) => {
    const wasSelected = selected.find((item) => item.uuid === option.uuid);
    onChange(
      wasSelected
        ? selected.filter((item) => item.uuid !== option.uuid)
        : selected.concat(option)
    );
    return !wasSelected;
  };

  const showAddNewOption = useMemo(() => {
    if (!allowNew) return false;
    // Only show the "Add '<searchTerm>'" option when:
    // 1. We have a valid search term string
    if (searchTermTrimmed.length === 0) return false;
    // 2. The to-be-added option doesn't already exist
    if (selected.find((item) => item.name == searchTermTrimmed)) return false;
    if (
      options.find((option) => {
        const searchTermLower = searchTermTrimmed.toLocaleLowerCase();
        return (
          option.name.toLocaleLowerCase() === searchTermLower ||
          option.uuid.toLocaleLowerCase() === searchTermLower
        );
      })
    ) {
      return false;
    }
    return true;
  }, [allowNew, options, searchTermTrimmed, selected]);

  const [cursor, setCursor] = useState(0);
  const cursorRange = filteredOptions.length + (showAddNewOption ? 1 : 0);

  const moveCursorUp = () => {
    setCursor((prev) => {
      if (prev === 0) return prev;
      return prev - 1;
    });
  };
  const moveCursorDown = () => {
    setCursor((prev) => {
      if (prev + 1 >= cursorRange) return prev;
      return prev + 1;
    });
  };

  useEffect(() => {
    setCursor(0);
  }, [searchTerm]);

  const hasMultipleClientTypes =
    new Set(
      options.flatMap((option) =>
        option.type === "client" ? [option.clientType] : []
      )
    ).size > 1;

  // Row renderer for virtualized list
  const OptionRow = ({
    index,
    style,
  }: {
    index: number;
    style: React.CSSProperties;
  }) => {
    // If we're at the end and have the "add new" option
    if (showAddNewOption && index === filteredOptions.length) {
      return (
        <div style={style}>
          <Button
            className={cn(
              "w-full gap-2 [&>svg]:h-4 [&>svg]:w-4 [&>svg]:shrink-0",
              cursor === filteredOptions.length && "bg-accent"
            )}
            variant="ghost"
            value={searchTerm}
            onClick={() => {
              addNewOption();
              clearInputAndFocus();
            }}
          >
            <PlusIcon />
            <span className="grow overflow-hidden text-ellipsis whitespace-nowrap text-start">
              Add "{searchTermTrimmed}"
            </span>
            <CheckIcon className={cn("ml-auto shrink-0 opacity-0")} />
          </Button>
        </div>
      );
    }

    // Regular option
    const option = filteredOptions[index];
    if (!option) return null;

    const item = option.item;
    const type: TagOptionKey = (item.type || "unknown") as TagOptionKey;

    return (
      <div style={style}>
        <Button
          key={item.uuid}
          className={cn(
            "w-full gap-2 overflow-hidden text-ellipsis whitespace-nowrap [&>svg]:h-4 [&>svg]:w-4",
            index === cursor && "bg-accent"
          )}
          variant="ghost"
          value={item.uuid}
          onClick={() => {
            const didSelect = toggleOptionSelection(item);
            if (didSelect) {
              clearInputAndFocus();
            }
            setOpen(true);
            // Ensure that the search input is focused after selecting an option, so the user
            // can continue searching for more attendees.
            if (searchInputRef.current) {
              searchInputRef.current.focus();
            }
          }}
        >
          {type === "client" ? (
            <ContactsSharp />
          ) : type === "user" ? (
            <Person4Sharp />
          ) : (
            <Help />
          )}
          <span className="grow overflow-hidden text-ellipsis whitespace-nowrap text-start">
            <Highlight
              ranges={option.highlightRanges ?? null}
              text={item.name}
            />
          </span>
          <Badge
            className={tagOptions[type].style}
            key={item.uuid}
            inline="inline"
            variant={"secondary"}
          >
            <div className="flex flex-row gap-0.5">
              {tagOptions[type].icon}
              {attendeeTagTitle(
                item,
                enableShowClientType && hasMultipleClientTypes
              )}
            </div>
          </Badge>
          {selected.find((i) => i.uuid === item.uuid) && (
            <CheckIcon className={cn("ml-auto shrink-0", "opacity-100")} />
          )}
        </Button>
      </div>
    );
  };

  // Calculate the total number of items to render (filtered options + possibly the add new option)
  const totalItemCount = filteredOptions.length + (showAddNewOption ? 1 : 0);

  // Calculate the height for the virtualized list
  const listHeight = Math.min(totalItemCount * itemSize, maxListHeight);

  return (
    <Popover
      open={open}
      onOpenChange={(isOpen) => {
        // Clear the search term when the popover is closed, so that the next time it is opened it
        // will start fresh.
        if (!isOpen) {
          setSearchTerm("");
        }
        setOpen(isOpen);
      }}
      modal={modal}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between rounded-2xl p-3 text-base",
            "h-fit min-h-fit",
            triggerClassName
          )}
          aria-describedby={ariaDescribedBy}
          onClick={() => setOpen(!open)}
        >
          {leftIcon}
          {(() => {
            if (selected.length === 0) {
              return (
                <Typography
                  className="inline-flex grow"
                  color="secondary"
                  asChild
                >
                  <span>{placeholder}</span>
                </Typography>
              );
            }
            return (
              <span className="inline-flex grow flex-wrap gap-2">
                {selected.map((item) => {
                  const selectedOption = options.find(
                    ({ uuid }) => uuid === item.uuid
                  );
                  if (!selectedOption) return null;
                  return (
                    <AttendeeOptionTag
                      key={selectedOption.uuid} // Ensure each child has a unique key
                      attendeeOption={selectedOption}
                      onDelete={() =>
                        onChange(selected.filter((i) => i !== item))
                      }
                    />
                  );
                })}
              </span>
            );
          })()}

          <CaretSortIcon className="h-6 w-6 shrink-0 self-baseline" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          "w-fit min-w-[var(--radix-popper-anchor-width)] p-0",
          commandClassName
        )}
      >
        <Input
          ref={searchInputRef}
          placeholder="Search..."
          className="border-none bg-transparent shadow-none hover:bg-transparent has-[input:focus]:ring-0"
          type="search"
          leftIcon={<SearchOutlined />}
          onChange={(event) => setSearchTerm(event.currentTarget.value)}
          onKeyDown={(event) => {
            const item = filteredOptions[cursor]?.item ?? null;
            switch (event.key) {
              case "Enter": {
                if (item) {
                  const didSelect = toggleOptionSelection(item);
                  if (didSelect) {
                    clearInputAndFocus();
                  }
                } else {
                  addNewOption();
                  clearInputAndFocus();
                }
                setOpen(true);
                return;
              }

              case "ArrowUp": {
                event.preventDefault();
                moveCursorUp();
                return;
              }
              case "ArrowDown": {
                event.preventDefault();
                moveCursorDown();
                return;
              }

              default: {
                return;
              }
            }
          }}
        />
        <Separator />
        <div className="p-1">
          {options.length === 0 && searchTerm.trim().length === 0 ? (
            <div className="py-6 text-center text-sm font-normal text-secondary">
              {emptyLabel}
            </div>
          ) : totalItemCount > 0 ? (
            <FixedSizeList
              height={listHeight}
              width="100%"
              itemCount={totalItemCount}
              itemSize={itemSize}
            >
              {OptionRow}
            </FixedSizeList>
          ) : (
            <div className="py-6 text-center text-sm font-normal text-secondary">
              {emptyLabel}
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};
