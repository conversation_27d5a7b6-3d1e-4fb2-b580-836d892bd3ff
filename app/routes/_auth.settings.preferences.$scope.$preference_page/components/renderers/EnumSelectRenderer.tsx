import React from "react";
import { ControlProps, isEnumControl, rankWith } from "@jsonforms/core";
import { withJsonFormsControlProps } from "@jsonforms/react";
import { CircleHelp } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
} from "~/@shadcn/ui/select";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

/**
 * Renderer for enum controls using ShadCN Select component
 */
const EnumSelectRenderer = (props: ControlProps) => {
  const {
    data,
    handleChange,
    path,
    label,
    schema,
    required,
    id,
    enabled,
    errors,
    uischema,
  } = props;

  const hasDescription = schema.description && schema.description.length > 0;
  const options = schema.enum || [];
  const labels = uischema.options?.enumLabels || options;

  const onChange = (value: string) => {
    handleChange(path, value);
  };

  return (
    <div className="mt-6 flex flex-col">
      <div className="mb-2 flex items-center gap-2">
        <label htmlFor={id} className="font-medium">
          {label}
          {required && <span className="text-red-500">*</span>}
        </label>
        {hasDescription && (
          <Tooltip>
            <TooltipTrigger asChild>
              <CircleHelp className="h-4 w-4 cursor-help text-gray-400" />
            </TooltipTrigger>
            <TooltipContent className="max-w-xs text-sm">
              {schema.description}
            </TooltipContent>
          </Tooltip>
        )}
      </div>
      <Select
        onValueChange={onChange}
        defaultValue={data || ""}
        disabled={!enabled}
      >
        <SelectTrigger className="mt-1 w-80" id={id}>
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {options.map((option: string, index: number) => (
              <SelectItem key={option} value={option}>
                {labels[index] || option}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
      {errors && (
        <div className="mt-2 text-xs font-normal text-red-500">{errors}</div>
      )}
    </div>
  );
};

// This tester will be used to determine when to use this renderer
// It matches any enum control with a high rank of 10
export const enumSelectRendererTester = rankWith(20, isEnumControl);

export default withJsonFormsControlProps(EnumSelectRenderer);
