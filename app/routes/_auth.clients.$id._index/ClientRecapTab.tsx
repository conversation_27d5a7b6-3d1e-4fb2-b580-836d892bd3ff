import { Link } from "@remix-run/react";
import { Typography } from "~/@ui/Typography";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "~/@shadcn/ui/tooltip";
import { ClientRecap } from "~/api/openapi/generated";
import { SerializeFrom } from "@remix-run/node";

export const ClientRecapTab = ({
  recap,
}: {
  recap: SerializeFrom<ClientRecap> | null;
}) => {
  const categories = recap?.recap || [];
  const references = recap?.references || [];

  return (
    <>
      {categories.length > 0 ? (
        <ol className="flex list-decimal flex-col gap-2 pl-4">
          {categories.map((category) => (
            <li
              key={category.topic}
              className="whitespace-pre-wrap text-warning"
            >
              <Typography
                className="text-xl font-semibold leading-9"
                variant="h4"
                color="primary"
                asChild
              >
                <span>{category.topic}</span>
              </Typography>
              <ul className="flex list-disc flex-col gap-2 pl-4 pt-2">
                {category.bullets.map((bullet, bulletIndex) => (
                  <li
                    key={bulletIndex}
                    className="whitespace-pre-wrap text-warning"
                  >
                    <Typography
                      className="border-none text-lg !opacity-100 focus:outline-none"
                      variant="body2"
                      color="default"
                      asChild
                    >
                      <div className="inline-block align-top">
                        {bullet.text}
                        {bullet.references && bullet.references.length > 0 && (
                          <div className="inline-block">
                            {bullet.references.map((refIndex) => {
                              const reference = references[refIndex];
                              return reference && reference.link ? (
                                <TooltipProvider key={refIndex}>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Link
                                        to={reference.link}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className={`ml-1 cursor-pointer align-top text-xs ${
                                          reference.source === "Zeplyn"
                                            ? "text-warning"
                                            : "text-primary"
                                        }`}
                                      >
                                        [{refIndex + 1}]
                                      </Link>
                                    </TooltipTrigger>
                                    {reference.source === "Zeplyn" && (
                                      <TooltipContent
                                        className="max-w-md break-words rounded-md bg-white p-4 text-black shadow-lg"
                                        sideOffset={5}
                                      >
                                        <Typography
                                          variant="body3"
                                          color="default"
                                        >
                                          {reference?.hoverText}
                                        </Typography>
                                      </TooltipContent>
                                    )}
                                  </Tooltip>
                                </TooltipProvider>
                              ) : null;
                            })}
                          </div>
                        )}
                      </div>
                    </Typography>
                  </li>
                ))}
              </ul>
            </li>
          ))}
        </ol>
      ) : (
        <div className="mt-4 flex flex-col items-center">
          <Typography className="mb-4 text-center" color="secondary">
            No client recap information available.
          </Typography>
        </div>
      )}
    </>
  );
};
