import { useEffect, useState } from "react";
import { ArrowBackOutlined, SaveOutlined } from "@mui/icons-material";
import { LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useFetcher, <PERSON> } from "@remix-run/react";
import { LoaderCircle } from "lucide-react";

import { Configuration, SettingsApi } from "~/api/openapi/generated";
import { configurationParameters } from "~/api/openapi/configParams";
import { Button } from "~/@shadcn/ui/button";

import SettingTypeWidget from "./components/SettingTypeWidget";

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  const config = new Configuration(await configurationParameters(request));

  // get subpath without the leading and trailing slashes
  const identifier = (params["*"] || "").replace(/^\/+|\/+$/g, "");

  const settingsDetailsData = await new SettingsApi(
    config
  ).settingsGetSettingsDetailsRoute({ identifier });

  return settingsDetailsData;
};

const Route = () => {
  const settingsDetailsData = useLoaderData<typeof loader>();
  const fetcher = useFetcher();
  const [settingsData, setSettingsData] = useState({}); // data that's sent to BE on clicking Save
  const [saveStatusMessage, setSaveStatusMessage] = useState("");

  const saveResponse = fetcher.data as { success: boolean } | undefined;

  useEffect(() => {
    if (!saveResponse) {
      return;
    }

    const { success } = saveResponse;
    setSaveStatusMessage(
      success ? "Changes saved!" : "Failed to save changes. Please try again"
    );
  }, [saveResponse]);

  const { label, data, showSaveButton } = settingsDetailsData;
  const firstDataId = data[0]?.id;

  // on data change, refresh settingsData & clear save status message
  useEffect(() => {
    setSettingsData({});
    setSaveStatusMessage("");
  }, [firstDataId]);

  // when user clicks the save CTA, remove save status msg & submit the form
  const savePageSettings = () => {
    setSaveStatusMessage("");

    const formData = new FormData();

    formData.append("settings-data", JSON.stringify(settingsData));

    fetcher.submit(formData, {
      method: "post",
      encType: "multipart/form-data",
    });
  };

  // when user changes a setting, remove save status msg & update the settingsData object
  const updateSettingsObject = (id: string, value: any) => {
    setSaveStatusMessage("");

    setSettingsData((prevSettings) => ({
      ...prevSettings,
      [id]: value,
    }));
  };

  const isSaveInProgress = fetcher.state === "submitting";

  return (
    <>
      <div className="flex items-center">
        <Button className="mr-2 md:hidden" size="icon-sm" variant="outline">
          <Link to="/settings">
            <ArrowBackOutlined />
          </Link>
        </Button>
        <h2 className="text-2xl font-semibold">{label}</h2>
      </div>

      {data.map((item: any) => (
        <SettingTypeWidget
          key={item.id}
          item={item}
          onChange={updateSettingsObject}
        />
      ))}

      {showSaveButton && (
        <div className="mt-6 flex items-center">
          <Button
            size="default"
            disabled={isSaveInProgress}
            onClick={savePageSettings}
          >
            {!isSaveInProgress ? (
              <SaveOutlined />
            ) : (
              <LoaderCircle className="animate-spin" />
            )}
            <span>{isSaveInProgress ? "Saving..." : "Save Changes"}</span>
          </Button>

          {saveStatusMessage && (
            <span
              className={`ml-4 text-sm ${
                saveResponse?.success ? "text-success" : "text-warning"
              }`}
            >
              {saveStatusMessage}
            </span>
          )}
        </div>
      )}
    </>
  );
};

export default Route;
