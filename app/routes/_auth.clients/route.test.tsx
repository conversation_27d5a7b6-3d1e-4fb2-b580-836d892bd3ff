import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { createRemixStub } from "@remix-run/testing";
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest";

import Route from "./route";
import { TooltipProvider } from "~/@shadcn/ui/tooltip";
import { useFlag } from "~/context/flags";

// Mock the useFlag hook
vi.mock("~/context/flags", () => ({
  useFlag: vi.fn(),
}));

// Mock fetch globally
global.fetch = vi.fn();

beforeAll(() => {
  vi.mock("react-window", () => ({
    FixedSizeList: ({ children, itemCount, onItemsRendered: onItems }: any) => {
      const simulateScroll = () => {
        if (onItems) {
          onItems({
            visibleStartIndex: itemCount - 2,
            visibleStopIndex: itemCount - 1,
            overscanStartIndex: itemCount - 2,
            overscanStopIndex: itemCount - 1,
          });
        }
      };

      const items = [];
      for (let i = 0; i < itemCount; i++) {
        items.push(
          <div key={i} data-testid={`virtualized-item-${i}`}>
            {children({ index: i, style: {} })}
          </div>
        );
      }

      return (
        <div data-testid="virtualized-list">
          {items}
          <button data-testid="simulate-scroll" onClick={simulateScroll}>
            Simulate Scroll
          </button>
        </div>
      );
    },
  }));

  vi.mock("react-virtualized-auto-sizer", () => ({
    default: ({ children }: { children: any }) =>
      children({ width: 800, height: 600 }),
  }));
});

const mockClients = [
  { uuid: "client-1", name: "Client One", type: "individual" },
  { uuid: "client-2", name: "Client Two", type: "business" },
  { uuid: "client-3", name: "Client Three", type: "individual" },
];

describe("Clients Route", () => {
  beforeEach(() => {
    vi.mocked(useFlag).mockReturnValue(true);
    vi.mocked(fetch).mockResolvedValue({
      json: () => Promise.resolve({
        clients: mockClients,
        nextPageToken: "next-page-token",
      }),
    } as Response);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("renders the clients list correctly", async () => {
    const RemixStub = createRemixStub([
      {
        path: "/clients",
        Component: Route,
      },
    ]);

    render(
      <TooltipProvider>
        <RemixStub initialEntries={["/clients"]} />
      </TooltipProvider>
    );

    await screen.findByTestId("virtualized-list");

    expect(screen.getByText("Client One")).toBeInTheDocument();
    expect(screen.getByText("Client Two")).toBeInTheDocument();
    expect(screen.getByText("Client Three")).toBeInTheDocument();
  });

  it("displays 'Nothing here... yet.' when no clients are found", async () => {
    vi.mocked(fetch).mockResolvedValueOnce({
      json: () => Promise.resolve({
        clients: [],
        nextPageToken: "",
      }),
    } as Response);

    const RemixStub = createRemixStub([
      {
        path: "/clients",
        Component: Route,
      },
    ]);

    render(
      <TooltipProvider>
        <RemixStub initialEntries={["/clients"]} />
      </TooltipProvider>
    );

    await screen.findByText("Nothing here... yet.");
  });

  it("allows searching for clients", async () => {
    const user = userEvent.setup();

    const searchResults = [
      { uuid: "client-2", name: "Client Two", type: "business" },
    ];

    // Mock fetch to return different results based on search term
    vi.mocked(fetch)
      .mockResolvedValueOnce({
        json: () => Promise.resolve({
          clients: mockClients,
          nextPageToken: "next-page-token",
        }),
      } as Response)
      .mockResolvedValueOnce({
        json: () => Promise.resolve({
          clients: searchResults,
          nextPageToken: "",
        }),
      } as Response);

    const RemixStub = createRemixStub([
      {
        path: "/clients",
        Component: Route,
      },
    ]);

    render(
      <TooltipProvider>
        <RemixStub initialEntries={["/clients"]} />
      </TooltipProvider>
    );

    await screen.findByText("Client One");

    const searchInput = screen.getByTestId("search-input");
    await user.type(searchInput, "Two");

    // Wait for search results to update
    await waitFor(() =>
      expect(screen.queryByText("Client One")).not.toBeInTheDocument()
    );
    expect(screen.getByText("Client Two")).toBeInTheDocument();
    expect(screen.queryByText("Client Three")).not.toBeInTheDocument();
  });

  it("shows loading indicator during search", async () => {
    const user = userEvent.setup();

    // Mock a delayed response to ensure the spinner is visible
    vi.mocked(fetch)
      .mockResolvedValueOnce({
        json: () => Promise.resolve({
          clients: mockClients,
          nextPageToken: "next-page-token",
        }),
      } as Response)
      .mockImplementationOnce(
        () =>
          new Promise((resolve) => {
            // Delay the response to ensure the spinner is visible
            setTimeout(() => {
              resolve({
                json: () => Promise.resolve({
                  clients: [{ uuid: "client-1", name: "Client One", type: "individual" }],
                  nextPageToken: "",
                }),
              } as Response);
            }, 100);
          })
      );

    const RemixStub = createRemixStub([
      {
        path: "/clients",
        Component: Route,
      },
    ]);

    render(
      <TooltipProvider>
        <RemixStub initialEntries={["/clients"]} />
      </TooltipProvider>
    );

    await screen.findByText("Client One");

    const searchInput = screen.getByTestId("search-input");
    await user.type(searchInput, "Test");

    await waitFor(() => {
      const searchLoadingIcon = screen.getByTestId("search-spinner-icon");
      expect(searchLoadingIcon).toBeInTheDocument();
    });
  });

  it("allows clearing the search query", async () => {
    const user = userEvent.setup();

    const RemixStub = createRemixStub([
      {
        path: "/clients",
        Component: Route,
      },
    ]);

    render(
      <TooltipProvider>
        <RemixStub initialEntries={["/clients"]} />
      </TooltipProvider>
    );

    await screen.findByText("Client One");

    const searchInput = screen.getByTestId("search-input");
    await user.type(searchInput, "Test");

    const clearButton = screen.getByTestId("clear-search-icon");
    await user.click(clearButton);

    expect(searchInput).toHaveValue("");
  });

  it("loads more clients when scrolling to the bottom", async () => {
    // Clients for first page
    const initialClients = [
      { uuid: "client-1", name: "Client One", type: "individual" },
      { uuid: "client-2", name: "Client Two", type: "business" },
    ];

    // Clients for second page
    const additionalClients = [
      { uuid: "client-3", name: "Client Three", type: "individual" },
      { uuid: "client-4", name: "Client Four", type: "business" },
    ];

    // Mock fetch to return initial clients first, then additional clients
    vi.mocked(fetch)
      .mockResolvedValueOnce({
        json: () => Promise.resolve({
          clients: initialClients,
          nextPageToken: "next-page-token",
        }),
      } as Response)
      .mockResolvedValueOnce({
        json: () => Promise.resolve({
          clients: additionalClients,
          nextPageToken: "",
        }),
      } as Response);

    const RemixStub = createRemixStub([
      {
        path: "/clients",
        Component: Route,
      },
    ]);

    render(
      <TooltipProvider>
        <RemixStub initialEntries={["/clients"]} />
      </TooltipProvider>
    );

    await screen.findByText("Client One");
    expect(screen.getByText("Client Two")).toBeInTheDocument();

    const initialClientCards = screen.getAllByTestId("client-card-wrapper");
    expect(initialClientCards.length).toBe(2);

    const scrollButton = await screen.findByTestId("simulate-scroll");
    const user = userEvent.setup();
    await user.click(scrollButton);

    await screen.findByText("Client Three");
    expect(screen.getByText("Client Four")).toBeInTheDocument();

    const allClientCards = screen.getAllByTestId("client-card-wrapper");
    expect(allClientCards.length).toBe(4);
  });

  it("doesn't load more clients when cursor is empty", async () => {
    // Mock fetch to return clients with empty cursor
    vi.mocked(fetch).mockResolvedValue({
      json: () => Promise.resolve({
        clients: mockClients,
        nextPageToken: "",
      }),
    } as Response);

    const RemixStub = createRemixStub([
      {
        path: "/clients",
        Component: Route,
      },
    ]);

    render(
      <TooltipProvider>
        <RemixStub initialEntries={["/clients"]} />
      </TooltipProvider>
    );

    await screen.findByText("Client One");

    expect(screen.getByText("Client Two")).toBeInTheDocument();
    expect(screen.getByText("Client Three")).toBeInTheDocument();

    const initialClientCards = screen.getAllByTestId("client-card-wrapper");
    const initialCount = initialClientCards.length;

    const initialFetchCalls = vi.mocked(fetch).mock.calls.length;

    // Simulate scrolling to the bottom by clicking the button we added to our mock
    const scrollButton = await screen.findByTestId("simulate-scroll");
    const user = userEvent.setup();
    await user.click(scrollButton);

    await waitFor(() => {
      const finalClientCards = screen.getAllByTestId("client-card-wrapper");
      expect(finalClientCards.length).toBe(initialCount);
    });

    // Should not make additional fetch calls since cursor is empty
    expect(vi.mocked(fetch).mock.calls.length).toBe(initialFetchCalls);
  });

  it("doesn't attempt pagination when the feature flag is disabled", async () => {
    vi.mocked(useFlag).mockReturnValue(false);

    const RemixStub = createRemixStub([
      {
        path: "/clients",
        Component: Route,
      },
    ]);

    render(
      <TooltipProvider>
        <RemixStub initialEntries={["/clients"]} />
      </TooltipProvider>
    );

    await screen.findByText("Client One");

    // Verify that fetch was called with pageSize=0 when pagination is disabled
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining("pageSize=0")
    );
  });
});
