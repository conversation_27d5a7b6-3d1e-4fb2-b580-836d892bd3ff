import { SerializeFrom, type MetaFunction } from "@remix-run/node";
import {
  useNavigate,
  useOutletContext,
  useRevalidator,
} from "@remix-run/react";
import { HeaderV2, SidebarV2 } from "~/@ui/layout/LayoutV2";
import { useTailwindBreakpoints } from "~/utils/useTailwindBreakpoints";
import { useEffect } from "react";
import { Typography } from "~/@ui/Typography";
import { ApiRoutersCrmClientResponse } from "~/api/openapi/generated";

type ClientsContextType = {
  clients: SerializeFrom<ApiRoutersCrmClientResponse[]>;
};

// Helpers
const useRedirectToFirstClientOnDesktop = () => {
  const navigate = useNavigate();
  const { matchedBreakpoints } = useTailwindBreakpoints();
  const { clients } = useOutletContext<ClientsContextType>();
  const firstClientId = clients[0]?.uuid ?? undefined;

  const redirectToFirstClient = matchedBreakpoints.has("lg");
  useEffect(() => {
    if (firstClientId && redirectToFirstClient)
      navigate(`/clients/${firstClientId}`);
  }, [navigate, firstClientId, redirectToFirstClient]);

  return { firstClientId };
};

// Exports
export const meta: MetaFunction = () => [
  { title: "Clients" },
  { name: "description", content: "View all clients" },
];

const Route = () => {
  // On desktop, we "pre-select" the first note by redirecting to /note/:firstClientId
  const { firstClientId } = useRedirectToFirstClientOnDesktop();

  const { revalidate } = useRevalidator();
  useEffect(() => {
    if (firstClientId) revalidate();
  }, [firstClientId, revalidate]);

  return (
    <SidebarV2 header={<HeaderV2 subtitle="Note details" />}>
      <div className="flex flex-col gap-3 self-stretch px-5">
        <Typography
          className="my-8 text-center"
          color="secondary"
          variant="body2"
        >
          {firstClientId ? "Loading client..." : "No client selected."}
        </Typography>
      </div>
    </SidebarV2>
  );
};
export default Route;
