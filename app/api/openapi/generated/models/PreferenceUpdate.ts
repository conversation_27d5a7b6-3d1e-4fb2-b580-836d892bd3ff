/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface PreferenceUpdate
 */
export interface PreferenceUpdate {
    /**
     * Scope of the preference update
     * @type {string}
     * @memberof PreferenceUpdate
     */
    scope: PreferenceUpdateScopeEnum;
    /**
     * Name of the preference object to update
     * @type {string}
     * @memberof PreferenceUpdate
     */
    schemaTitle: string;
    /**
     * Values to update in the preference object
     * @type {object}
     * @memberof PreferenceUpdate
     */
    updatedValues: object;
}


/**
 * @export
 */
export const PreferenceUpdateScopeEnum = {
    User: 'user',
    Organization: 'organization'
} as const;
export type PreferenceUpdateScopeEnum = typeof PreferenceUpdateScopeEnum[keyof typeof PreferenceUpdateScopeEnum];


/**
 * Check if a given object implements the PreferenceUpdate interface.
 */
export function instanceOfPreferenceUpdate(value: object): value is PreferenceUpdate {
    if (!('scope' in value) || value['scope'] === undefined) return false;
    if (!('schemaTitle' in value) || value['schemaTitle'] === undefined) return false;
    if (!('updatedValues' in value) || value['updatedValues'] === undefined) return false;
    return true;
}

export function PreferenceUpdateFromJSON(json: any): PreferenceUpdate {
    return PreferenceUpdateFromJSONTyped(json, false);
}

export function PreferenceUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): PreferenceUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'scope': json['scope'],
        'schemaTitle': json['schema_title'],
        'updatedValues': json['updated_values'],
    };
}

export function PreferenceUpdateToJSON(value?: PreferenceUpdate | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'scope': value['scope'],
        'schema_title': value['schemaTitle'],
        'updated_values': value['updatedValues'],
    };
}

