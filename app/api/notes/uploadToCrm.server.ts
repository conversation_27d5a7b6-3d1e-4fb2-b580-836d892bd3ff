import { logError } from "~/utils/log.server";
import {
  Configuration,
  CrmApi,
  CRMUploadTarget,
  ResponseError,
} from "../openapi/generated";
import { configurationParameters } from "../openapi/configParams";

export type UploadNoteToCrmArguments = {
  noteId: string;
  request: Request;
  uploadTargetID?: string;
};

export const uploadNoteToCrm = async ({
  noteId,
  request,
  uploadTargetID,
}: UploadNoteToCrmArguments): Promise<{
  success: boolean;
  userInputRequired?: boolean;
  uploadTargetOptions?: CRMUploadTarget[];
  error?: string;
}> => {
  try {
    const config = new Configuration(await configurationParameters(request));
    const response = await new CrmApi(config).crmUploadNoteToCrm({
      noteId,
      syncTargetIds: uploadTargetID ? [uploadTargetID] : [],
    });
    return {
      success: true,
      userInputRequired: response?.userInputRequired ?? false,
      uploadTargetOptions: response?.noteSyncTargets ?? undefined,
    };
  } catch (error) {
    logError("!!! CrmApi.crmUploadNoteToCrm", error);

    if (!(error instanceof ResponseError)) {
      return {
        success: false,
        error: "An unexpected error occurred",
      };
    }

    // Populate the default error messages based on the status code.
    let errorMessage: string | undefined = undefined;
    switch (error.response.status) {
      case 400:
        errorMessage = "Bad Request: Failed to sync note";
      case 403:
        errorMessage = "Forbidden: Not authorized to sync this note";
      case 404:
        errorMessage = `Note with ID ${noteId} not found`;
    }

    // Check to see if there is a more-specific error message in the response body.
    try {
      let data = await error.response.json();
      errorMessage = "detail" in data ? data?.detail : undefined;
    } catch (parseError) {
      logError(
        "!!! CrmApi.crmUploadNoteToCrm - error parsing detail from error",
        parseError
      );
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
};
