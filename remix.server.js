import * as fs from "node:fs";
import * as path from "node:path";
import * as url from "node:url";
import { createRe<PERSON><PERSON><PERSON><PERSON> } from "@remix-run/express";
import { broadcastDevReady, installGlobals } from "@remix-run/node";
import compression from "compression";
import express from "express";
import morgan from "morgan";
import sourceMapSupport from "source-map-support";
import { loadEnv } from "./loadEnv.js";
import consoleStamp from "console-stamp";
import { trace, context } from "@opentelemetry/api";

// Add timestamps to the beginning of all messages logged to the console. This
// is a workaround: it is difficult to control all usages of console.X() in the
// codebase, so rather than attempt to do so, we ensure that all usages of
// console have a consistent timestamp so that we can accurately determine when
// a new log entry has started.
consoleStamp(console, {
  // The date and label formats match the console stamp defaults.
  format:
    ":date(dd.mm.yyyy HH:MM.ss.l) :label(7) [trace_id=:traceId() span_id=:spanId()]",
  tokens: {
    traceId: () => {
      return (
        trace.getSpan(context.active())?.spanContext().traceId ??
        "00000000000000000000000000000000"
      );
    },
    spanId: () => {
      return (
        trace.getSpan(context.active())?.spanContext().spanId ??
        "0000000000000000"
      );
    },
  },
});

// Load environment variables
loadEnv();

/* ANSIcolor codes used when logging to terminal */
const SUCCESS_PREFIX = "\x1b[44m\x1b[42m done \x1b[0m";
const INFO_PREFIX = "\x1b[44m\x1b[30m info \x1b[0m";

sourceMapSupport.install({
  retrieveSourceMap: function (source) {
    const match = source.startsWith("file://");
    if (match) {
      const filePath = url.fileURLToPath(source);
      const sourceMapPath = `${filePath}.map`;
      if (fs.existsSync(sourceMapPath)) {
        return {
          url: source,
          map: fs.readFileSync(sourceMapPath, "utf8"),
        };
      }
    }
    return null;
  },
});
installGlobals();

// Ensure that unhandled promises in loaders don't crash the server.
// cf https://github.com/remix-run/remix/issues/9178, https://github.com/remix-run/remix/issues/765
// Modified from code at https://github.com/remix-run/remix/issues/765#issuecomment-2192517799
process.on("unhandledRejection", (reason, p) => {
  // eslint-disable-next-line no-console
  console.error(
    "Unhandled Rejection at:",
    p,
    "reason:",
    reason,
    "stacktrace:",
    reason?.stack ?? "<none>"
  );
});

/** @typedef {import('@remix-run/node').ServerBuild} ServerBuild */

const BUILD_PATH = path.resolve("build/index.js");
const VERSION_PATH = path.resolve("build/version.txt");

const initialBuild = await reimportServer();
const remixHandler =
  process.env.NODE_ENV === "development"
    ? await createDevRequestHandler(initialBuild)
    : createRequestHandler({
        build: initialBuild,
        mode: initialBuild.mode,
      });

const app = express();

app.use(compression());

// http://expressjs.com/en/advanced/best-practice-security.html#at-a-minimum-disable-x-powered-by-header
app.disable("x-powered-by");

// Return 404 errors for requests to sourcemaps.
app.get("/build", (req, res, next) => {
  if (req.path.endsWith(".js.map") || req.path.endsWith(".css.map")) {
    return res.sendStatus(404);
  }
  next();
});

// Remix fingerprints its assets so we can cache forever.
app.use(
  "/build",
  express.static("public/build", { immutable: true, maxAge: "1y" })
);

// Everything else (like favicon.ico) is cached for an hour. You may want to be
// more aggressive with this caching.
app.use(express.static("public", { maxAge: "1h" }));

app.use(
  morgan("tiny", {
    stream: {
      write: function (str) {
        /* eslint-disable no-console */
        console.info(str.trim());
        /* eslint-enable no-console */
      },
    },
  })
);

app.all("*", remixHandler);

const port = process.env.PORT || 3000;
app.listen(port, async () => {
  /* eslint-disable no-console */
  console.log(SUCCESS_PREFIX, `🚀 Express server listening on port ${port}.`);
  console.log(INFO_PREFIX, `NODE_ENV=${process.env.NODE_ENV}`);
  console.log(INFO_PREFIX, `ZEPLYN_LOG_LEVEL=${process.env.ZEPLYN_LOG_LEVEL}`);
  console.log(
    INFO_PREFIX,
    `💡 Set ZEPLYN_LOG_LEVEL=info npm run dev to see detailed network logs.`
  );
  /* eslint-enable no-console */

  if (process.env.NODE_ENV === "development") {
    broadcastDevReady(initialBuild);
  }
});

/**
 * @returns {Promise<ServerBuild>}
 */
async function reimportServer() {
  const stat = fs.statSync(BUILD_PATH);

  // convert build path to URL for Windows compatibility with dynamic `import`
  const BUILD_URL = url.pathToFileURL(BUILD_PATH).href;

  // use a timestamp query parameter to bust the import cache
  return import(BUILD_URL + "?t=" + stat.mtimeMs);
}

/**
 * @param {ServerBuild} initialBuild
 * @returns {Promise<import('@remix-run/express').RequestHandler>}
 */
async function createDevRequestHandler(initialBuild) {
  let build = initialBuild;
  async function handleServerUpdate() {
    // 1. re-import the server build
    build = await reimportServer();
    // 2. tell Remix that this app server is now up-to-date and ready
    broadcastDevReady(build);
  }
  const chokidar = await import("chokidar");
  chokidar
    .watch(VERSION_PATH, { ignoreInitial: true })
    .on("add", handleServerUpdate)
    .on("change", handleServerUpdate);

  // wrap request handler to make sure its recreated with the latest build for every request
  return async (req, res, next) => {
    try {
      return createRequestHandler({
        build,
        mode: "development",
      })(req, res, next);
    } catch (error) {
      next(error);
    }
  };
}
