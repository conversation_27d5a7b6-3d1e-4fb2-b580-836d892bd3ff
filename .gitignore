# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build
/public/build

/.react-router

# misc
.DS_Store
.env.development.local
.env.development.secrets
.cache

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local IDE configs
.vscode/settings.json
.vscode/vscode-npm-prepare-hook-events.log
pnpm-lock.yaml
